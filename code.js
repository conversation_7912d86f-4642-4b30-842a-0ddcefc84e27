var B=Object.defineProperty,H=Object.defineProperties;var V=Object.getOwnPropertyDescriptors;var C=Object.getOwnPropertySymbols;var X=Object.prototype.hasOwnProperty,z=Object.prototype.propertyIsEnumerable;var L=(a,e,t)=>e in a?B(a,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[e]=t,A=(a,e)=>{for(var t in e||(e={}))X.call(e,t)&&L(a,t,e[t]);if(C)for(var t of C(e))z.call(e,t)&&L(a,t,e[t]);return a},x=(a,e)=>H(a,V(e));async function O(a,e={}){const r=A(A({},{format:"PNG",scale:4,useMaxResolution:!0,quality:1,svgOutlineText:!0,svgSimplifyStroke:!1}),e);let n;if(r.useMaxResolution){const o=Math.max(a.width,a.height);n={type:"SCALE",value:Math.min(r.scale,4096/o)}}else n={type:"SCALE",value:r.scale};let s;switch(r.format){case"JPG":s={format:"JPG",constraint:n,quality:r.quality};break;case"SVG":s={format:"SVG",constraint:n,svgOutlineText:r.svgOutlineText,svgSimplifyStroke:r.svgSimplifyStroke};break;case"PDF":s={format:"PDF",constraint:n};break;default:s={format:"PNG",constraint:n}}try{const o=await a.exportAsync(s);return{bytes:o,base64:figma.base64Encode(o),dataUrl:`data:image/${r.format.toLowerCase()};base64,${figma.base64Encode(o)}`}}catch(o){console.error(`Export failed for node ${a.id}:`,o);const c=await a.exportAsync({format:"PNG",constraint:{type:"SCALE",value:3}});return{bytes:c,base64:figma.base64Encode(c),dataUrl:`data:image/png;base64,${figma.base64Encode(c)}`,usedFallback:!0}}}function W(a){const e=Math.max(a.width,a.height);if(e<500)return{scale:6};if(e<1e3)return{scale:4};{const r=Math.floor(4e3/e);return{scale:Math.max(2,r)}}}const Y=()=>{let a={quality:"high",format:"PNG",useAdaptiveQuality:!0};figma.ui.onmessage=async e=>{try{switch(e.type){case"PLUGIN:SET_EXPORT_QUALITY":a=A(A({},a),e.payload),figma.ui.postMessage({type:"STATE:EXPORT_SETTINGS_UPDATED",payload:a});break;case"PLUGIN:EXPORT_ELEMENT":try{const{nodeId:t,format:r,scale:n,previewStatus:s,isMaximumResolution:o}=e.payload,c=await le(t,r,n,s,o);figma.ui.postMessage({type:"STATE:ELEMENT_EXPORTED",payload:{nodeId:t,dataUrl:c}})}catch(t){console.error("Error handling element export:",t),figma.ui.postMessage({type:"ERROR",payload:t instanceof Error?t.message:"Unknown error"})}break;case"PLUGIN:CHANGE_UI_SIZE":await j(e.payload);break;case"PLUGIN:GET_ALL_FRAMES":await q();break;case"PLUGIN:LOAD_SELECTED_FRAMES":await Q(e);break;case"PLUGIN:GET_FRAME_CHILDREN":await Z(e);break;case"PLUGIN:GET_NESTED_FRAME_CHILDREN":await ge(e);break;case"PLUGIN:SAVE_PRESET":await J(e.payload);break;case"PLUGIN:EXIT":case"PLUGIN:UPDATE_TEXT_CONTENT":await K(e);break;case"PLUGIN:UPDATE_TEXT_CONTENT_V2":await se(e);break;case"PLUGIN:REPLACE_IMAGE":await te(e);break;case"PLUGIN:SELECTION_CHANGE":await ae(e);break;case"PLUGIN:SELECT_NODE":await ie(e);break;case"PLUGIN:GET_CURRENT_SELECTION":await ce();break;default:console.warn("Unknown message type:",e.type)}}catch(t){console.error("Error handling message:",t),figma.ui.postMessage({type:"ERROR",payload:t.message})}},figma.on("selectionchange",()=>{const e=figma.currentPage.selection,t=v(e);figma.ui.postMessage({type:"STATE:SELECTION_CHANGED",payload:t})})};async function j(a){figma.ui.resize(a.width,a.height)}async function q(){const a=figma.currentPage.children.filter(e=>e.type==="FRAME").map(e=>x(A({},e),{id:e.id,name:e.name,width:e.width,height:e.height,type:e.type,includeInExport:!0,children:e.children.map(t=>x(A({},t),{id:t.id,name:t.name,type:t.type,width:t.width,height:t.height,x:t.x,y:t.y}))}));figma.ui.postMessage({type:"STATE:ALL_FRAMES",payload:a})}async function Q(a){console.log("handleLoadSelectedFrames msg payload:",a.payload);const e=a.payload.map(r=>figma.getNodeById(r.frameId)).filter(Boolean);console.log(`Processing ${e.length} frames`);const t=await Promise.all(e.map(async r=>{const n=await O(r,{format:"PNG",useMaxResolution:!0,scale:W(r).scale}),s={id:r.id,name:r.name,type:r.type,width:r.width,height:r.height,elements:[],preview:n.dataUrl,includeInExport:!0,absoluteBoundingBox:r.absoluteBoundingBox||void 0,absoluteRenderBounds:r.absoluteRenderBounds||void 0},o=figma.getNodeById(r.id);if(o&&o.type==="FRAME"){const c=await Promise.all(o.children.map(async m=>await F(m,!1)));s.elements=c.flat()}else console.log("no frame element found");return s}));e.length===a.payload.length&&(console.log("Sending messages to UI with frame data"),figma.ui.postMessage({type:"STATE:SHOW_ANIMATION_EDITOR",payload:t}),figma.ui.postMessage({type:"STATE:SET_FRAMES",payload:t}))}async function Z(a){const e=figma.getNodeById(a.payload.frameId);if(e&&e.type==="FRAME"){const r=(await Promise.all(e.children.map(async n=>await F(n,!1)))).flat();figma.ui.postMessage({type:"STATE:FRAME_CHILDREN_LOADED",payload:{frameId:e.id,children:r}})}}const _=a=>{try{return a&&a.id&&figma.getNodeById(a.id)!==null}catch(e){return!1}};async function F(a,e=!0){const t=[],r=async(n,s=!0,o=null,c=null,m=null)=>{var G;const i=async(T,D=!0)=>{let w=null;try{w=T.clone();const R=T.id;if(w&&w.id!==R){if(w!=null&&w.children&&w.children.length>0&&D===!1){const k=[...w.children];await Promise.all(k.map(async P=>{try{_(P)&&(P.visible=!1)}catch($){console.warn(`Could not remove child ${(P==null?void 0:P.name)||"unknown"}: ${$}`)}}))}const S=await w.exportAsync({format:"PNG",constraint:{type:"SCALE",value:3}});return figma.base64Encode(S)}else{console.warn("Clone operation failed or produced invalid clone");const S=await T.exportAsync({format:"PNG",constraint:{type:"SCALE",value:3}});return figma.base64Encode(S)}}catch(R){console.error("Error generating frame image:",R);try{const S=await T.exportAsync({format:"PNG",constraint:{type:"SCALE",value:3}});return figma.base64Encode(S)}catch(S){return console.error("Fallback export also failed:",S),""}}finally{if(w&&w.id!==T.id)try{w.remove()}catch(R){console.error("Error during clone cleanup:",R)}}},y=await i(n),p=await i(n),h=await i(n,!1);let u=n.x,f=n.y,l=o,g=c;const d=n.type,E=m==null?void 0:m.type;d==="FRAME"&&E==="FRAME"?(u=l?n.x+l:n.x,f=g?n.y+g:n.y,l=l?l+n.x:n.x,g=g?g+n.y:n.y):d==="FRAME"&&E==="GROUP"?(u=n.x+l-m.x,f=n.y+g-m.y,l=u,g=f):d==="GROUP"&&E==="FRAME"?(u=l?n.x+l:n.x,f=g?n.y+g:n.y,l=l?l+n.x:n.x,g=g?g+n.y:n.y):d==="GROUP"&&E==="GROUP"||(d!=="GROUP"||d!=="FRAME")&&E==="GROUP"?(u=n.x+l-m.x,f=n.y+g-m.y,l=u,g=f):(u=l?n.x+l:n.x,f=g?n.y+g:n.y,l=l?l+n.x:n.x,g=g?g+n.y:n.y);const I={id:n.id,name:n.name,type:n.type,x:u,y:f,width:n.width,height:n.height,selected:e,preview:`data:image/png;base64,${y}`,previewWithVisibleChildren:`data:image/png;base64,${p}`,previewWithoutVisibleChildren:`data:image/png;base64,${h}`,previewStatus:"visible",animation:{entrance:{type:"noAnimation",loop:!1,delay:0,speed:1},exit:{type:"noAnimation",loop:!1,delay:0,speed:1}},characters:n.type==="TEXT"?n.characters:void 0,children:n.children};if(t.push(I),((G=n==null?void 0:n.children)==null?void 0:G.length)>0)for(const T of n.children)await r(T,s,o=l,c=g,n)};return await r(a,!0),t}async function J(a){try{const e=await figma.clientStorage.getAsync("animationPresets")||[];e.push(a),await figma.clientStorage.setAsync("animationPresets",e)}catch(e){throw console.error("Error saving preset:",e),new Error("Failed to save preset")}}async function K(a){try{const{frameId:e,updates:t}=a.payload,r=figma.getNodeById(e);if(!r||r.type!=="FRAME")throw new Error("Invalid frame");const n=[],s=[];for(const o of t)try{console.log("Processing update for node:",o.id);const c=figma.getNodeById(o.id);if(!c||c.type!=="TEXT"){console.warn("Node not found or not a text node:",o.id),s.push({id:o.id,error:"Node not found or not a text node",name:c==null?void 0:c.name});continue}try{await figma.loadFontAsync(c.fontName),c.characters=o.text,c.setPluginData("translatedLanguage",o.language),n.push(c.id),console.log("Successfully updated node:",o.id)}catch(m){const i=m instanceof Error?m.message:"Unknown font error";console.error(`Font loading error for ${c.name}:`,i),s.push({id:c.id,error:`Font not available: ${i}`,name:c.name}),figma.ui.postMessage({type:"ERROR:FONT_LOADING",payload:{nodeId:c.id,nodeName:c.name,error:`Cannot update text - font not available: ${i}`}})}}catch(c){console.error(`Failed to update node ${o.id}:`,c),s.push({id:o.id,error:c instanceof Error?c.message:"Unknown error"})}figma.ui.postMessage({type:"STATE:UPDATE_TEXT_CONTENT_SUCCESS",payload:{frameId:e,successfulUpdates:n,failedUpdates:s}}),n.length>0,s.length>0&&figma.ui.postMessage({type:"STATE:UPDATE_ERRORS",payload:{frameId:e,frameName:r.name,failedUpdates:s.map(o=>({id:o.id,name:o.name||o.id,tag:"unknown",error:o.error}))}})}catch(e){throw console.error("Failed to update text content:",e),figma.ui.postMessage({type:"ERROR",payload:e.message}),e}}function M(a){if(a.type==="RECTANGLE"||a.type==="ELLIPSE"||a.type==="POLYGON"||a.type==="STAR"){const e=a.fills;if(e!==figma.mixed&&Array.isArray(e))return e.some(t=>t.type==="IMAGE")}return!1}async function ee(a){try{const e=await figma.clientStorage.getAsync("imageHistory")||[],t=[a,...e].slice(0,10);await figma.clientStorage.setAsync("imageHistory",t)}catch(e){console.error("Error saving URL to history:",e)}}async function te(a){const{url:e,nodeIds:t}=a.payload;let r=[];if(t&&t.length>0?r=t.map(s=>figma.getNodeById(s)).filter(s=>s!==null):r=[...figma.currentPage.selection],!(r.length===0||!r.some(s=>M(s))))try{const o=await(await fetch(e)).arrayBuffer(),c=figma.createImage(new Uint8Array(o));for(const m of r)if(M(m)){const i=m.fills;if(i!==figma.mixed&&Array.isArray(i)&&i.length>0){const y=i.map(p=>p.type==="IMAGE"?x(A({},p),{imageHash:c.hash}):p);m.fills=y}}await ee(e)}catch(s){s instanceof Error&&s.message}}function v(a){if(a.length===0)return{count:0,types:["No layers selected"],nodeIds:[]};const e=a.map(t=>t.type==="TEXT"?"Text Layer":t.type==="RECTANGLE"||t.type==="ELLIPSE"||t.type==="POLYGON"?"Shape Layer":t.type==="FRAME"||t.type==="GROUP"?"Frame/Group":t.type==="INSTANCE"||t.type==="COMPONENT"?"Component/Instance":M(t)?"Image Layer":t.type);return{count:a.length,types:e,nodeIds:a.map(t=>t.id)}}async function ae(a){const{selection:e}=a.payload,t=v(e);figma.ui.postMessage({type:"STATE:SELECTION_INFO",payload:t})}async function ne(a,e){const t=[],r=n=>{n.name.includes(e)&&t.push(n),"children"in n&&n.children.forEach(s=>{r(s)})};return r(a),t}function re(a,e){switch(e){case"top-to-bottom":return[...a].sort((t,r)=>t.y-r.y);case"bottom-to-top":return[...a].sort((t,r)=>r.y-t.y);case"left-to-right":return[...a].sort((t,r)=>t.x-r.x);case"right-to-left":return[...a].sort((t,r)=>r.x-t.x);case"layer-order":return a;case"layer-order-reverse":return[...a].reverse();case"name-asc":return[...a].sort((t,r)=>t.name.localeCompare(r.name));case"name-desc":return[...a].sort((t,r)=>r.name.localeCompare(t.name));default:return a}}async function oe(a,e,t={}){const r=t.layout||"vertical",n=t.spacing||20;t.margin;const s=t.columns||3,o=a.clone();if(r==="vertical")o.x=a.x,o.y=a.y+(a.height+n)*e;else if(r==="horizontal")o.x=a.x+(a.width+n)*e,o.y=a.y;else if(r==="grid"){const c=Math.floor(e/s),m=e%s;o.x=a.x+m*(a.width+n),o.y=a.y+c*(a.height+n)}return o.name=`${a.name} (${e+1})`,o}async function se(a){try{const{frameIds:e,tags:t,updateData:r,orderStrategy:n,isAutoRepeat:s,layout:o="vertical",spacing:c=20,margin:m=20,columns:i=3,createGroup:y=!1}=a.payload;if(!e||e.length===0)throw new Error("No frames selected");if(!t||t.length===0)throw new Error("No tags specified");if(!r||r.length===0)throw new Error("No update data provided");const p=await Promise.all(e.map(async h=>{const u=figma.getNodeById(h);if(!u||u.type!=="FRAME")throw new Error(`Invalid frame: ${h}`);if(!s||r.length<=1){const f=await b(u,t,r[0]||{},n);return{frameId:h,updates:f}}else{const f=[],l=[],g=await b(u,t,r[0],n);l.push({frameId:u.id,updates:g});for(let d=1;d<r.length;d++){const E=await oe(u,d,{layout:o,spacing:c,margin:m,columns:i,createGroup:y});f.push(E);const I=await b(E,t,r[d],n);l.push({frameId:E.id,updates:I})}if(y&&f.length>0){const d=[u,...f],E=figma.group(d,figma.currentPage);return E.name=`${u.name} Group`,{frameId:h,updates:l,createdFrames:f.map(I=>({id:I.id,name:I.name})),group:{id:E.id,name:E.name}}}return{frameId:h,updates:l,createdFrames:f.map(d=>({id:d.id,name:d.name}))}}}));figma.ui.postMessage({type:"STATE:UPDATE_TEXT_CONTENT_V2_SUCCESS",payload:{results:p}})}catch(e){throw console.error("Failed to update text content:",e),figma.ui.postMessage({type:"ERROR",payload:e.message}),e}}async function b(a,e,t,r){const n=[],s=[];for(const o of e){if(!t[o]&&!t[o.replace("#","")]){console.warn(`No data for tag ${o}`);continue}const c=await ne(a,o),m=re(c,r);for(const i of m)try{if(i.type==="TEXT")try{const y=t[o]||t[o.replace("#","")];await figma.loadFontAsync(i.fontName),i.characters=y,i.setPluginData("updatedWithTag",o),n.push({id:i.id,name:i.name,tag:o,text:y})}catch(y){const p=y instanceof Error?y.message:"Unknown font error";console.error(`Font loading error for node ${i.name}:`,p),s.push({id:i.id,name:i.name,tag:o,error:`Font not available: ${p}`}),figma.ui.postMessage({type:"ERROR:FONT_LOADING",payload:{nodeId:i.id,nodeName:i.name,tag:o,error:`Cannot update text - font not available: ${p}`}})}if(M(i)){const y=t[o]||t[o.replace("#","")];if(y.startsWith("http"))try{const h=await(await fetch(y)).arrayBuffer(),u=figma.createImage(new Uint8Array(h)),f=i;if(f.fills!==figma.mixed&&Array.isArray(f.fills)){const l=f.fills.map(g=>g.type==="IMAGE"?x(A({},g),{imageHash:u.hash}):g);f.fills=l,n.push({id:i.id,name:i.name,tag:o,imageUrl:y})}}catch(p){const h=p instanceof Error?p.message:"Unknown error";console.error(`Failed to load image for node ${i.id}:`,p),s.push({id:i.id,name:i.name,tag:o,error:`Image loading failed: ${h}`}),figma.ui.postMessage({type:"ERROR:IMAGE_LOADING",payload:{nodeId:i.id,nodeName:i.name,tag:o,error:`Failed to load image: ${h}`}})}}}catch(y){const p=y instanceof Error?y.message:"Unknown error";console.error(`Failed to update node ${i.id}:`,y),s.push({id:i.id,name:i.name,tag:o,error:p})}}return s.length>0&&figma.ui.postMessage({type:"STATE:UPDATE_ERRORS",payload:{frameId:a.id,frameName:a.name,failedUpdates:s}}),n}async function ie(a){try{const{nodeId:e}=a.payload,t=figma.getNodeById(e);if(!t){console.warn(`Node with ID ${e} not found`);return}"visible"in t?(figma.currentPage.selection=[t],"scrollIntoView"in figma.viewport&&figma.viewport.scrollAndZoomIntoView([t])):console.warn(`Node with ID ${e} is not a selectable node`)}catch(e){throw console.error("Error selecting node:",e),e}}async function ce(){try{const a=figma.currentPage.selection,e=v(a);figma.ui.postMessage({type:"STATE:SELECTION_CHANGED",payload:e})}catch(a){throw console.error("Error getting current selection:",a),a}}async function le(a,e="PNG",t=1,r,n){try{const s=figma.getNodeById(a);if(!s)throw new Error("Node not found");const o=s.clone();if(r==="unvisible"&&o!=null&&o.children&&(o==null?void 0:o.children.length)>0){const m=[...o.children];await Promise.all(m.map(async i=>{try{_(i)&&(i.visible=!1)}catch(y){console.warn(`Could not remove child ${(i==null?void 0:i.name)||"unknown"}: ${y}`)}}))}if(n){const{dataUrl:m}=await O(o,{useMaxResolution:!0,format:e});return m}const c=await o.exportAsync({format:e,constraint:{type:"SCALE",value:t}});return`data:image/${e.toLowerCase()};base64,${figma.base64Encode(c)}`}catch(s){throw console.error("Error exporting element:",s),s}}async function ge(a){const e=figma.getNodeById(a.payload.frameId);if(e&&e.type==="FRAME")try{let t=function(s){const o={id:s.id,name:s.name,type:s.type,x:s.x,y:s.y,width:s.width,height:s.height,isAnimatable:!0};return s.type==="TEXT"&&(o.characters=s.characters),"children"in s?s.type==="FRAME"&&r.has(s.id)?(o.isReferenced=!0,o):(s.type==="FRAME"&&r.add(s.id),o.children=s.children.map(c=>t(c)),o.hasChildren=s.children.length>0,o):o};const r=new Set,n=t(e);figma.ui.postMessage({type:"STATE:FRAME_CHILDREN_LOADED_V2",payload:{frameId:e.id,children:n.children||[],frame:{id:e.id,name:e.name,width:e.width,height:e.height}}})}catch(t){console.error("Error processing nested frame children:",t),figma.ui.postMessage({type:"ERROR",payload:{message:"Failed to load frame children",details:t instanceof Error?t.message:String(t)}})}}const me=async()=>{fe();const a=await ue();figma.ui.postMessage({type:"PLUGIN_INITIALIZED",payload:A({isInitialized:!0},a)}),ye()};function fe(){figma.on("selectionchange",U(()=>{try{const e=figma.currentPage.selection.map(t=>({id:t.id,name:t.name,type:t.type,isAnimatable:N(t)}));figma.ui.postMessage({type:"SELECTION_CHANGE",payload:{selection:e,timestamp:Date.now()}})}catch(a){console.error("Selection change handler failed:",a)}},100)),figma.on("documentchange",U(({documentChanges:a})=>{try{const e=a.filter(t=>pe(t));e.length>0&&figma.ui.postMessage({type:"DOCUMENT_CHANGE",payload:{changes:e,timestamp:Date.now()}})}catch(e){console.error("Document change handler failed:",e)}},100)),figma.on("currentpagechange",()=>{try{figma.ui.postMessage({type:"PAGE_CHANGE",payload:{pageId:figma.currentPage.id,pageName:figma.currentPage.name}})}catch(a){console.error("Page change handler failed:",a)}})}async function ye(){try{const a=await figma.clientStorage.getAsync("savedAnimations");a&&figma.ui.postMessage({type:"LOAD_SAVED_ANIMATIONS",payload:a})}catch(a){console.error("Error loading saved data:",a)}}async function ue(){try{const[a,e,t,r]=await Promise.all([figma.clientStorage.getAsync("animations"),figma.clientStorage.getAsync("presets"),figma.clientStorage.getAsync("settings"),figma.clientStorage.getAsync("recentProjects")]),n=figma.currentPage.selection,s=n.length>0?n[0]:null;return{animations:await de(a),presets:e||[],settings:t||Ee(),recentProjects:r||[],selectedNode:s?{id:s.id,name:s.name,type:s.type}:null,hasSeenSplash:await figma.clientStorage.getAsync("hasSeenSplash")||!1}}catch(a){throw console.error("Failed to load initial data:",a),new Error("Data loading failed")}}function N(a){return["FRAME","GROUP","COMPONENT","INSTANCE"].includes(a.type)}function pe(a){const e=["effects","constraints","opacity","size","position","rotation"];return a.type==="PROPERTY_CHANGE"&&a.properties.some(t=>e.includes(t))}async function de(a){if(!a)return{};const e={};for(const[t,r]of Object.entries(a)){const n=figma.getNodeById(t);n&&N(n)&&(e[t]=r)}return await figma.clientStorage.setAsync("animations",e),e}function Ee(){return{defaultDuration:1,defaultEasing:"ease-in-out",exportQuality:"high",autoPreview:!0,showTimeline:!0,theme:"system",gridSize:8,snapToGrid:!0,showHints:!0}}function U(a,e){let t;return(...r)=>{const n=()=>{t=void 0,a(...r)};clearTimeout(t),t=setTimeout(n,e)}}figma.showUI(__html__,{width:1200,height:400,themeColors:!0,title:"Design Ops Plugin"});me();Y();
